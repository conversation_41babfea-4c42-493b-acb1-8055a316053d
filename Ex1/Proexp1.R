# 韵律图美化版本
# Enhanced Prosody Visualization

# 加载必要的库
library(readxl)
library(ggplot2)
library(lme4)
library(lattice)
library(emmeans)
library(cowplot)
library(ggpubr)
library(lmerTest)
library(scales)  # 用于更好的坐标轴格式化
library(RColorBrewer)  # 用于更好的颜色方案

# 读取数据
intonation <- read_excel('normf1.xlsx')
intonation$point <- as.numeric(intonation$point)

# 创建美化的韵律图
prosody_plot <- ggplot(data = intonation, aes(x = point, y = f00)) +
  # 主要线条 - 使用更粗的线条和渐变色
  geom_line(linewidth = 2.5, color = "#2E86AB", alpha = 0.9) +
  # 添加数据点
  geom_point(size = 3, color = "#A23B72", alpha = 0.8) +
  # 使用现代主题
  theme_minimal(base_size = 14) +
  # 自定义主题设置
  theme(
    # 背景设置
    panel.background = element_rect(fill = "white", color = NA),
    plot.background = element_rect(fill = "white", color = NA),

    # 网格线设置
    panel.grid.major = element_line(color = "grey90", linewidth = 0.5, linetype = "dotted"),
    panel.grid.minor = element_blank(),

    # 坐标轴设置
    axis.line = element_line(color = "grey30", linewidth = 0.8),
    axis.ticks = element_line(color = "grey30", linewidth = 0.6),
    axis.text = element_text(color = "grey20", size = 12),
    axis.title = element_text(color = "grey10", size = 14, face = "bold"),

    # 标题设置
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", color = "grey10"),
    plot.subtitle = element_text(hjust = 0.5, size = 12, color = "grey30"),

    # 图例设置
    legend.position = "none",

    # 边距设置
    plot.margin = margin(20, 20, 20, 20)
  ) +

  # 坐标轴设置
  scale_x_continuous(
    limits = c(0, 9),
    breaks = seq(0, 9, 1),
    labels = rep("", 10),
    expand = c(0.02, 0.02)
  ) +
  scale_y_continuous(
    breaks = pretty_breaks(n = 6),
    expand = c(0.05, 0.05)
  ) +

  # 坐标轴标签
  labs(
    x = "音节 (Syllable)",
    y = "基频 (Pitch, Hz)",
    title = "韵律轮廓图",
    subtitle = "每只猫吃着一盆猫粮"
  )
# 添加音节标签 - 使用更美观的样式
syllable_labels <- c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
syllable_positions <- seq(0.5, 8.5, 1)

# 为每个音节添加标签，使用不同的颜色和样式
for (i in 1:length(syllable_labels)) {
  prosody_plot <- prosody_plot +
    annotate("text",
             x = syllable_positions[i],
             y = 155,  # 稍微调低位置
             label = syllable_labels[i],
             vjust = 0,
             size = 4.5,
             color = "#2E86AB",
             fontface = "bold",
             family = "sans")
}

# 添加音节分隔线（可选）
prosody_plot <- prosody_plot +
  geom_vline(xintercept = seq(1, 8, 1),
             color = "grey80",
             linetype = "dashed",
             alpha = 0.5,
             linewidth = 0.3)

# 显示图形
print(prosody_plot)

# 保存高质量图片
ggsave(
  plot = prosody_plot,
  filename = "ProsodyExp1_Enhanced.png",
  width = 10,
  height = 6,
  units = "in",
  dpi = 300,
  bg = "white"
)

# 也保存PDF版本以便学术使用
ggsave(
  plot = prosody_plot,
  filename = "ProsodyExp1_Enhanced.pdf",
  width = 10,
  height = 6,
  units = "in",
  device = "pdf"
)
# 创建一个更简洁的版本（无网格线）
prosody_plot_clean <- ggplot(data = intonation, aes(x = point, y = f00)) +
  geom_line(linewidth = 3, color = "#2E86AB", alpha = 0.9) +
  geom_point(size = 4, color = "#A23B72", alpha = 0.8) +
  theme_void() +
  theme(
    plot.background = element_rect(fill = "white", color = NA),
    axis.line.x = element_line(color = "grey30", linewidth = 0.8),
    axis.line.y = element_line(color = "grey30", linewidth = 0.8),
    axis.text.x = element_blank(),
    axis.text.y = element_text(color = "grey20", size = 12),
    axis.title.x = element_text(color = "grey10", size = 14, face = "bold", margin = margin(t = 15)),
    axis.title.y = element_text(color = "grey10", size = 14, face = "bold", margin = margin(r = 15)),
    plot.title = element_text(hjust = 0.5, size = 16, face = "bold", color = "grey10", margin = margin(b = 20)),
    plot.margin = margin(30, 30, 30, 30)
  ) +
  scale_x_continuous(
    limits = c(0, 9),
    breaks = seq(0, 9, 1),
    labels = rep("", 10),
    expand = c(0.02, 0.02)
  ) +
  scale_y_continuous(
    breaks = pretty_breaks(n = 6),
    expand = c(0.05, 0.05)
  ) +
  labs(
    x = "音节 (Syllable)",
    y = "基频 (Pitch, Hz)",
    title = "韵律轮廓图 - 简洁版"
  )

# 为简洁版添加音节标签
for (i in 1:length(syllable_labels)) {
  prosody_plot_clean <- prosody_plot_clean +
    annotate("text",
             x = syllable_positions[i],
             y = 155,
             label = syllable_labels[i],
             vjust = 0,
             size = 5,
             color = "#2E86AB",
             fontface = "bold")
}

# 显示简洁版
print(prosody_plot_clean)

# 保存简洁版
ggsave(
  plot = prosody_plot_clean,
  filename = "ProsodyExp1_Clean.png",
  width = 10,
  height = 6,
  units = "in",
  dpi = 300,
  bg = "white"
)

cat("已生成以下美化版本的韵律图：\n")
cat("1. ProsodyExp1_Enhanced.png - 完整版（带网格线）\n")
cat("2. ProsodyExp1_Enhanced.pdf - PDF版本\n")
cat("3. ProsodyExp1_Clean.png - 简洁版（无网格线）\n")
cat("原始图片：ProsodyExp1.png\n")

