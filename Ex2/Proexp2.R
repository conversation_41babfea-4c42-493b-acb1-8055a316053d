library(readxl)
library(ggplot2)
library(lme4)
library(lattice)
library(emmeans)
library(cowplot)
library(ggpubr)
library(lmerTest)


intonation <-read_excel('normf2.xlsx')
intonation$point=as.numeric(intonation$point)

# 创建更美观和学术规范的韵律图
p <- ggplot(data=intonation, aes(x=point, y=f00)) +
  geom_line(linewidth=1.2, color="#1f77b4") +
  theme_bw(base_size=14) +
  xlab("Syllable") +
  ylab("Pitch (Hz)") +
  scale_x_continuous(
    limits = c(0, 9), 
    breaks = seq(0.5, 8.5, 1),
    labels = c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
  ) +
  theme(
    panel.grid.minor = element_blank(),
    panel.grid.major.x = element_blank(),
    panel.border = element_rect(color = "black", fill = NA, linewidth = 1),
    axis.text.x = element_text(size = 12, color = "black"),
    axis.text.y = element_text(size = 12, color = "black"),
    axis.title = element_text(size = 14, face = "bold"),
    plot.margin = margin(10, 10, 10, 10)
  ) +
  geom_vline(xintercept = c(0, 3, 9), color = "red", linetype = "dashed", linewidth = 1) +
  annotate("text", x = 1.5, y = 200, label = "Pitch Accent", size = 4, color = "red", fontface = "bold")

# 显示图形
p

# 保存高质量图片
ggsave(
  filename = "ProsodyExp2_improved.png",
  plot = p,
  width = 10,
  height = 6,
  units = "in",
  dpi = 300,
  bg = "white"
)