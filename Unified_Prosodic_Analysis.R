# ===============================================================================
# Unified Prosodic Analysis: Chinese Sentence "mei-zhi mao chi-zhe yipen maoliang"
# Three Stress Conditions: Neutral, Subject Stress, Object Stress
# Academic Standard Visualization for Linguistics Publication
# ===============================================================================

# Load required libraries
library(readxl)
library(ggplot2)
library(cowplot)
library(scales)
library(grid)
library(gridExtra)

# Set global parameters for academic publication standards
FIGURE_WIDTH <- 10
FIGURE_HEIGHT <- 6
COMBINED_WIDTH <- 15
DPI_RESOLUTION <- 300
BASE_FONT_SIZE <- 12
TITLE_FONT_SIZE <- 14
LABEL_FONT_SIZE <- 10

# Academic color palette (grayscale for publication)
PITCH_COLOR <- "#2c3e50"
ACCENT_COLOR <- "#e74c3c"
GRID_COLOR <- "#ecf0f1"
TEXT_COLOR <- "#2c3e50"

# Syllable information
SYLLABLES <- c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
SYLLABLE_POSITIONS <- seq(0.5, 8.5, 1)

# Stress condition definitions
STRESS_CONDITIONS <- list(
  neutral = list(
    title = "Neutral Prosody",
    subtitle = "No stress condition",
    accent_start = NULL,
    accent_end = NULL
  ),
  subject = list(
    title = "Subject Stress",
    subtitle = "Stress on 'mei-zhi mao'",
    accent_start = 0,
    accent_end = 3
  ),
  object = list(
    title = "Object Stress", 
    subtitle = "Stress on 'yi-pen maoliang'",
    accent_start = 5,
    accent_end = 9
  )
)

# ===============================================================================
# Function: Load and validate data
# ===============================================================================
load_prosodic_data <- function(file_path, condition_name) {
  tryCatch({
    data <- read_excel(file_path)
    data$point <- as.numeric(data$point)
    data$condition <- condition_name
    
    # Validate required columns
    if (!all(c("point", "f00") %in% names(data))) {
      stop(paste("Required columns 'point' and 'f00' not found in", file_path))
    }
    
    return(data)
  }, error = function(e) {
    stop(paste("Error loading", file_path, ":", e$message))
  })
}

# ===============================================================================
# Function: Create standardized prosodic plot
# ===============================================================================
create_prosodic_plot <- function(data, condition_key, show_title = TRUE) {
  condition_info <- STRESS_CONDITIONS[[condition_key]]
  
  # Calculate y-axis limits for consistent scaling
  y_min <- floor(min(data$f00, na.rm = TRUE) / 10) * 10
  y_max <- ceiling(max(data$f00, na.rm = TRUE) / 10) * 10
  y_range <- y_max - y_min
  label_y <- y_max + (y_range * 0.05)
  
  # Create base plot
  p <- ggplot(data, aes(x = point, y = f00)) +
    # Main pitch contour
    geom_line(linewidth = 1.5, color = PITCH_COLOR, alpha = 0.9) +
    geom_point(size = 2, color = PITCH_COLOR, alpha = 0.8) +
    
    # Academic theme
    theme_minimal(base_size = BASE_FONT_SIZE) +
    theme(
      # Background
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      
      # Grid
      panel.grid.major.y = element_line(color = GRID_COLOR, linewidth = 0.5),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      
      # Axes
      axis.line = element_line(color = TEXT_COLOR, linewidth = 0.8),
      axis.ticks = element_line(color = TEXT_COLOR, linewidth = 0.6),
      axis.text = element_text(color = TEXT_COLOR, size = LABEL_FONT_SIZE),
      axis.title = element_text(color = TEXT_COLOR, size = BASE_FONT_SIZE, face = "bold"),
      
      # Title
      plot.title = element_text(hjust = 0.5, size = TITLE_FONT_SIZE, face = "bold", 
                               color = TEXT_COLOR, margin = margin(b = 5)),
      plot.subtitle = element_text(hjust = 0.5, size = LABEL_FONT_SIZE, 
                                  color = TEXT_COLOR, margin = margin(b = 15)),
      
      # Margins
      plot.margin = margin(20, 20, 20, 20)
    ) +
    
    # Axis settings
    scale_x_continuous(
      limits = c(0, 9),
      breaks = seq(0, 9, 1),
      labels = rep("", 10),
      expand = c(0.02, 0.02)
    ) +
    scale_y_continuous(
      limits = c(y_min, label_y + 10),
      breaks = pretty_breaks(n = 6),
      expand = c(0, 0)
    ) +
    
    # Labels
    labs(
      x = "Syllable",
      y = "Fundamental Frequency (Hz)"
    )
  
  # Add title if requested
  if (show_title) {
    p <- p + labs(
      title = condition_info$title,
      subtitle = condition_info$subtitle
    )
  }
  
  # Add syllable labels
  for (i in 1:length(SYLLABLES)) {
    p <- p + annotate("text",
                     x = SYLLABLE_POSITIONS[i],
                     y = label_y,
                     label = SYLLABLES[i],
                     vjust = 0,
                     size = 3.5,
                     color = TEXT_COLOR,
                     fontface = "bold")
  }
  
  # Add stress marking if applicable
  if (!is.null(condition_info$accent_start) && !is.null(condition_info$accent_end)) {
    # Add accent boundary lines
    p <- p + 
      geom_vline(xintercept = condition_info$accent_start, 
                color = ACCENT_COLOR, linetype = "dashed", linewidth = 1.2, alpha = 0.8) +
      geom_vline(xintercept = condition_info$accent_end, 
                color = ACCENT_COLOR, linetype = "dashed", linewidth = 1.2, alpha = 0.8) +
      # Add stress label
      annotate("text", 
              x = (condition_info$accent_start + condition_info$accent_end) / 2,
              y = y_max - (y_range * 0.1),
              label = "Pitch Accent",
              size = 3,
              color = ACCENT_COLOR,
              fontface = "bold")
  }
  
  return(p)
}

# ===============================================================================
# Main Analysis
# ===============================================================================

# Load data from all three experiments
cat("Loading prosodic data...\n")
data_ex1 <- load_prosodic_data("Ex1/normf1.xlsx", "neutral")
data_ex2 <- load_prosodic_data("Ex2/normf2.xlsx", "subject_stress") 
data_ex3 <- load_prosodic_data("Ex3/normf3.xlsx", "object_stress")

# Calculate global y-axis limits for consistent scaling
all_data <- rbind(data_ex1, data_ex2, data_ex3)
global_y_min <- floor(min(all_data$f00, na.rm = TRUE) / 10) * 10
global_y_max <- ceiling(max(all_data$f00, na.rm = TRUE) / 10) * 10

cat("Data loaded successfully. Pitch range:", global_y_min, "-", global_y_max, "Hz\n")

# Create individual plots
cat("Generating individual prosodic plots...\n")
plot_neutral <- create_prosodic_plot(data_ex1, "neutral")
plot_subject <- create_prosodic_plot(data_ex2, "subject") 
plot_object <- create_prosodic_plot(data_ex3, "object")

# Create combined comparison plot
cat("Creating combined comparison figure...\n")
combined_plot <- plot_grid(
  plot_neutral, plot_subject, plot_object,
  ncol = 3,
  labels = c("A", "B", "C"),
  label_size = TITLE_FONT_SIZE,
  label_fontface = "bold"
)

# Add overall title to combined plot
title_grob <- textGrob(
  "Prosodic Contours of Chinese Sentence 'mei-zhi mao chi-zhe yipen maoliang'",
  gp = gpar(fontsize = TITLE_FONT_SIZE + 2, fontface = "bold")
)

subtitle_grob <- textGrob(
  "Comparison of Three Stress Conditions",
  gp = gpar(fontsize = BASE_FONT_SIZE, fontface = "plain")
)

combined_with_title <- grid.arrange(
  title_grob,
  subtitle_grob,
  combined_plot,
  heights = c(0.8, 0.4, 10),
  ncol = 1
)

# ===============================================================================
# Export Publication-Ready Figures
# ===============================================================================

cat("Exporting publication-ready figures...\n")

# Individual plots
ggsave("Figure1_Neutral_Prosody.pdf", plot_neutral, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION)
ggsave("Figure1_Neutral_Prosody.png", plot_neutral, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION, bg = "white")

ggsave("Figure2_Subject_Stress.pdf", plot_subject, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION)
ggsave("Figure2_Subject_Stress.png", plot_subject, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION, bg = "white")

ggsave("Figure3_Object_Stress.pdf", plot_object, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION)
ggsave("Figure3_Object_Stress.png", plot_object, 
       width = FIGURE_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION, bg = "white")

# Combined comparison plot
ggsave("Figure4_Combined_Comparison.pdf", combined_with_title, 
       width = COMBINED_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION)
ggsave("Figure4_Combined_Comparison.png", combined_with_title, 
       width = COMBINED_WIDTH, height = FIGURE_HEIGHT, dpi = DPI_RESOLUTION, bg = "white")

# ===============================================================================
# Summary Report
# ===============================================================================

cat("\n================================================================================\n")
cat("PROSODIC ANALYSIS COMPLETE\n")
cat("================================================================================\n")
cat("Generated Files:\n")
cat("- Figure1_Neutral_Prosody.pdf/.png (Neutral condition)\n")
cat("- Figure2_Subject_Stress.pdf/.png (Subject stress: mei-zhi mao)\n")
cat("- Figure3_Object_Stress.pdf/.png (Object stress: yi-pen maoliang)\n")
cat("- Figure4_Combined_Comparison.pdf/.png (All three conditions)\n")
cat("\nAll figures follow academic standards for linguistics publication.\n")
cat("Resolution: 300 DPI | Format: PDF (vector) + PNG (raster)\n")
cat("================================================================================\n")
